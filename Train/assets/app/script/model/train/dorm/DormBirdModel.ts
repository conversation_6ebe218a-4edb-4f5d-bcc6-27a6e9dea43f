import { CarriageUsePosType, RoleDir } from "../../../common/constant/Enums"
import AStar from "../../../common/helper/AStart"
import { gameHelper } from "../../../common/helper/GameHelper"
import { mapHelper } from "../../../common/helper/MapHelper"
import BaseMap from "../../map/BaseMap"
import { Shape } from "../../map/MoveModel"
import { StateType } from "../../passenger/StateEnum"
import StateObj from "../../passenger/StateObj"
import DormBirdAction from "../../passenger/themeActions/DormBirdAction"

export enum TragetType {
    UNKNOW,
    FLOOR, // 地面
    LEFT_CHAIR, // 左边树桩椅子
    RIGHT_CHAIR, // 右边树桩椅子
    RIGHT_BED, // 右边床的树杆
}

export default class DormBirdModel {

    public position: cc.Vec2 = null
    public action: DormBirdAction = null
    public lastSleepDay: number = 0
    public lastSleepEndTime: number = 0

    public shape: Shape = null
    private astar: AStar = new AStar()
    private tempVec1: cc.Vec2 = cc.v2()
    private tempVec2: cc.Vec2 = cc.v2()
    private area: BaseMap = null

    private _flyIn: boolean = false
    private _states: StateObj<StateType>[] = []
    private _carriageId: number = 1013
    private _skin: string
    private _dir: RoleDir = RoleDir.RIGHT
    private _isMoving: boolean = false
    private _currentTarget: TragetType = TragetType.UNKNOW

    get flyIn() { return this._flyIn }
    get skin() { return this._skin }
    get carriage() { return gameHelper.train.getCarriageById(this._carriageId) }
    get dir() { return this._dir }
    get isMoving() { return this._isMoving }
    set isMoving(v) { this._isMoving = v }
    get state() { return this._states.last() }
    get currentTarget() { return this._currentTarget }
    set currentTarget(v) { this._currentTarget = v }

    public static create(skin: string) {
        const model = new DormBirdModel()
        model._skin = skin
        model.initPos()
        const action = new DormBirdAction()
        action.setBy(model)
        model.action = action

        const map = model.carriage.getMap()
        model.area = map
        model.astar.setDir(8)
        model.astar.checkCanPass = (x: number, y: number, dir) => {
            return map.checkCanPass(x, y, model.shape, dir);
        }

        model.astar.area = map
        model.astar.mapHeight = map.size.height
        model.astar.mapWidth = map.size.width

        console.warn(`skin: ${skin}, pos: ${model.position}`)
        return model
    }


    public getPosition() { return this.position }
    public setPosition(v: cc.Vec2) { this.position.x = v.x; this.position.y = v.y }

    protected initPos() {
        let posList = this.carriage.getUsePosListByType(CarriageUsePosType.BIRD_IN)
        this.position = posList.random().pos
    }

    public getState(type: StateType) { return this._states.find(s => s.type == type) }

    public pushState(type: StateType, data = null) {
        let state = this._states.find(s => s.type == type)
        if (state) {
            twlog.error("pushState重复", this._skin, type)
            return
        }
        state = new StateObj<StateType>().init(type, data)
        this._states.push(state);
    }

    public popState(type: StateType) { this._states.remove("type", type) }

    update(dt: number) {
        this.action?.update(dt);
    }

    public searchPath(point: cc.Vec2) {
        this.tempVec1.set(point || this.area.getActPointByPixel(position))
        this.tempVec2.set(position || this.area.getActPixelByPoint(point))
        paths = await this.astar.searchPath(this.point, this.tempVec1)

        if (paths.length > 0) { //设置最后一个为像素位置
            paths.pop()
            paths.push(this.tempVec2.clone())
        } else if (this.tempVec1.equals(this.point) && !this.tempVec2.equals(this._position)) {
            paths.push(this.tempVec2.clone())
        }
    }

    public getRandomFlyPos() { return this._getCarriageRandomMovePos() }

    private _getCarriageRandomMovePos() {
        let map = this.carriage.getMap()
        let carriage = this.carriage
        // let emptyAreas = carriage.getEmptyAreas()
        // let areas = _areas || emptyAreas
        const areas = []
        // 获取当前车厢所有乘客的位置
        const posList = carriage.getPassengers().map(p => {
            let pos = p.getPosition()
            return pos
        })
        // 其他鸟儿的位置
        const birdPosList = this.carriage.birds.map(b => {
            let pos = b.getPosition()
            return pos
        })
        posList.pushArr(birdPosList)

        let randomFunc
        let retryCount = 30
        if (areas.length > 0) {
            //先选人数最少的区域
            let cnts = areas.map(area => {
                let cnt = 0
                for (let pos of posList) {
                    if (area.rect.contains(pos)) {
                        cnt++
                    }
                }
                return cnt
            })
            let min = cnts.min(c => c)
            let minAreas = []
            for (let i = 0; i < cnts.length; i++) {
                if (cnts[i] == min) minAreas.push(areas[i])
            }
            let area = minAreas.random()
            randomFunc = () => {
                let pos = cc.v2()
                while (retryCount--) {
                    let rdx = ut.randomRange(0, area.rect.width)
                    let rdy = ut.randomRange(0, area.rect.height)
                    pos.set2(area.rect.x + rdx, area.rect.y + rdy)
                    // let point = map.getActPointByPixel(pos)
                    return pos
                }
                return pos
            }
        }
        else { //没配空地，只能随机个看起来空的地方
            let reachablePoints = map.getConnectPoints(cc.v2());
            if (reachablePoints.length <= 1) { //与四周都不连通了
                reachablePoints = map.getMainEmptyPoints()
            }
            randomFunc = () => {
                let pos = cc.v2()
                while (retryCount--) {
                    let point = reachablePoints.random()
                    return map.getActPixelByPoint(point)
                }
                return pos
            }
        }

        //尽量找一个远离所有人的位置
        let pos = mapHelper.sparsePosRandom(randomFunc, posList, null, 50)
        return pos
    }

}